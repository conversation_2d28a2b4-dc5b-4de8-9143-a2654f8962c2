'use strict';

const userService = ({ strapi }) => ({
  async listUsers(query) {
    try {
      const { page = 1, pageSize = 10, search, blocked } = query;

      const filters = {};

      // Add search filter with sanitization
      if (search) {
        const sanitizedSearch = search
          .toString()
          .trim()
          .replace(/[<>'"\\{}[\]|&]/g, '') // Remove potentially problematic characters
          .substring(0, 100); // Limit length

        if (sanitizedSearch.length > 0) {
          filters.$or = [
            { name: { $containsi: sanitizedSearch } },
            { username: { $containsi: sanitizedSearch } },
            { email: { $containsi: sanitizedSearch } },
            { phone: { $containsi: sanitizedSearch } },
          ];
        }
      }

      // Add blocked filter
      if (blocked !== undefined) {
        filters.blocked = blocked === 'true';
      }

      const users = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        {
          filters,
          populate: {
            role: true,
            referUser: {
              fields: ['id', 'name', 'phone', 'email'],
            },
          },
          start: (page - 1) * pageSize,
          limit: pageSize,
          sort: { createdAt: 'desc' },
        }
      );

      const total = await strapi.entityService.count(
        'plugin::users-permissions.user',
        {
          filters,
        }
      );

      return {
        data: users,
        meta: {
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            pageCount: Math.ceil(total / pageSize),
            total,
          },
        },
      };
    } catch (error) {
      console.error('Error in listUsers:', error);
      throw error;
    }
  },

  async getUserDetail(id) {
    try {
      const user = await strapi.entityService.findOne(
        'plugin::users-permissions.user',
        id,
        {
          populate: {
            role: true,
            referUser: {
              fields: ['id', 'name', 'phone', 'email'],
            },
          },
        }
      );

      if (!user) {
        throw new Error('User not found');
      }

      // Get user orders
      const orders = await strapi.entityService.findMany(
        'api::don-hang.don-hang',
        {
          filters: { user: id },
          populate: {
            products: true,
            customer: true,
          },
          sort: { createdAt: 'desc' },
          limit: 100,
        }
      );

      // Get user commissions
      const commissions = await strapi.entityService.findMany(
        'api::hoa-hong.hoa-hong',
        {
          filters: { user: id },
          populate: {
            order: {
              fields: ['id', 'code'],
            },
          },
          sort: { createdAt: 'desc' },
          limit: 100,
        }
      );

      // Get user withdrawals
      const withdrawals = await strapi.entityService.findMany(
        'api::rut-tien.rut-tien',
        {
          filters: { user: id },
          sort: { createdAt: 'desc' },
          limit: 100,
        }
      );

      // Calculate statistics
      const totalOrders = orders.length;
      const completedOrders = orders.filter(
        (order) => order.statusOrder === 'Đã hoàn thành'
      ).length;
      const totalRevenue = orders
        .filter((order) => order.statusOrder === 'Đã hoàn thành')
        .reduce((sum, order) => sum + (order.priceAfterTax || 0), 0);
      const totalCommission = commissions
        .filter((commission) => commission.status === 'approved')
        .reduce((sum, commission) => sum + (commission.amount || 0), 0);

      return {
        user,
        orders,
        commissions,
        withdrawals,
        statistics: {
          totalOrders,
          completedOrders,
          totalRevenue,
          totalCommission,
        },
      };
    } catch (error) {
      console.error('Error in getUserDetail:', error);
      throw error;
    }
  },

  async updateUserStatus(id, blocked) {
    try {
      const updatedUser = await strapi.entityService.update(
        'plugin::users-permissions.user',
        id,
        {
          data: {
            blocked,
          },
        }
      );
      return updatedUser;
    } catch (error) {
      console.error('Error in updateUserStatus:', error);
      throw error;
    }
  },

  async updateUserInfo(id, userData) {
    try {
      const updatedUser = await strapi.entityService.update(
        'plugin::users-permissions.user',
        id,
        userData
      );
      return updatedUser;
    } catch (error) {
      console.error('Error in updateUserInfo:', error);
      throw error;
    }
  },

  async createUser(userData) {
    try {
      // Check if username already exists
      const existingUsername = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        {
          filters: { username: userData.username },
          limit: 1,
        }
      );

      if (existingUsername && existingUsername.length > 0) {
        throw new Error('Username already exists');
      }

      // Check if email already exists
      const existingEmail = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        {
          filters: { email: userData.email },
          limit: 1,
        }
      );

      if (existingEmail && existingEmail.length > 0) {
        throw new Error('Email already exists');
      }

      // Check if phone already exists
      const existingPhone = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        {
          filters: { phone: userData.phone },
          limit: 1,
        }
      );

      if (existingPhone && existingPhone.length > 0) {
        throw new Error('Phone number already exists');
      }

      // Find the authenticated role (default role for new users)
      let defaultRoleId = null;
      try {
        const roles = await strapi.entityService.findMany(
          'plugin::users-permissions.role',
          {
            fields: ['id', 'name', 'type'],
          }
        );

        console.log('Available roles:', roles); // Debug log

        // Look for authenticated role first, then public role as fallback
        const authenticatedRole = roles.find(
          (role) =>
            role.type === 'authenticated' ||
            role.name.toLowerCase() === 'authenticated'
        );

        const publicRole = roles.find(
          (role) =>
            role.type === 'public' || role.name.toLowerCase() === 'public'
        );

        if (authenticatedRole) {
          defaultRoleId = authenticatedRole.id;
        } else if (publicRole) {
          defaultRoleId = publicRole.id;
        } else if (roles.length > 0) {
          // Use the first available role as fallback
          defaultRoleId = roles[0].id;
        }

        console.log('Selected role ID:', defaultRoleId); // Debug log
      } catch (roleError) {
        console.error('Error fetching roles:', roleError);
      }

      // If no role found, throw error instead of using invalid ID
      if (!defaultRoleId) {
        throw new Error(
          'No valid user role found. Please ensure user roles are properly configured.'
        );
      }

      // Create user with default values
      const newUser = await strapi.entityService.create(
        'plugin::users-permissions.user',
        {
          data: {
            username: userData.username,
            email: userData.email,
            password: userData.password,
            name: userData.name,
            phone: userData.phone,
            role: defaultRoleId, // Use the found public role ID
            referUser: userData.referUser || null,
            confirmed: true,
            blocked: false,
            balance: 0,
          },
          populate: {
            role: true,
            referUser: {
              fields: ['id', 'name', 'phone', 'email'],
            },
          },
        }
      );

      return {
        success: true,
        data: newUser,
      };
    } catch (error) {
      console.error('Error in createUser:', error);
      throw error;
    }
  },

  async getUserRoles() {
    try {
      const roles = await strapi.entityService.findMany(
        'plugin::users-permissions.role',
        {
          fields: ['id', 'name', 'description'],
        }
      );

      return {
        success: true,
        data: roles,
      };
    } catch (error) {
      console.error('Error in getUserRoles:', error);
      throw error;
    }
  },

  async getUserStatistics() {
    try {
      const totalUsers = await strapi.entityService.count(
        'plugin::users-permissions.user'
      );
      const activeUsers = await strapi.entityService.count(
        'plugin::users-permissions.user',
        {
          filters: { blocked: false },
        }
      );
      const blockedUsers = await strapi.entityService.count(
        'plugin::users-permissions.user',
        {
          filters: { blocked: true },
        }
      );

      // Get new users this month
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const newUsersThisMonth = await strapi.entityService.count(
        'plugin::users-permissions.user',
        {
          filters: {
            createdAt: { $gte: startOfMonth },
          },
        }
      );

      // Get users by role
      const users = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        {
          populate: { role: true },
          fields: ['id'],
        }
      );

      const usersByRole = users.reduce((acc, user) => {
        const roleName = user.role?.name || 'Unknown';
        const existing = acc.find((item) => item.role === roleName);
        if (existing) {
          existing.count++;
        } else {
          acc.push({ role: roleName, count: 1 });
        }
        return acc;
      }, []);

      return {
        success: true,
        data: {
          totalUsers,
          activeUsers,
          blockedUsers,
          newUsersThisMonth,
          usersByRole,
        },
      };
    } catch (error) {
      console.error('Error in getUserStatistics:', error);
      throw error;
    }
  },

  async exportUsersToExcel(query) {
    try {
      const { search, blocked } = query;

      const filters = {};

      if (search) {
        const sanitizedSearch = search
          .toString()
          .trim()
          .replace(/[<>'"\\{}[\]|&]/g, '') // Remove potentially problematic characters
          .substring(0, 100); // Limit length

        if (sanitizedSearch.length > 0) {
          filters.$or = [
            { name: { $containsi: sanitizedSearch } },
            { username: { $containsi: sanitizedSearch } },
            { email: { $containsi: sanitizedSearch } },
            { phone: { $containsi: sanitizedSearch } },
          ];
        }
      }

      if (blocked !== undefined) {
        filters.blocked = blocked === 'true';
      }

      const users = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        {
          filters,
          populate: {
            role: true,
            referUser: {
              fields: ['id', 'name', 'phone', 'email'],
            },
          },
          sort: { createdAt: 'desc' },
          limit: 10000, // Large limit for export
        }
      );

      // Convert to CSV format
      const csvHeaders = [
        'STT',
        'ID',
        'Tên đại lý',
        'Tên đăng nhập',
        'Email',
        'Số điện thoại',
        'Trạng thái',
        'Người giới thiệu',
        'Ngày tạo',
      ];

      const csvRows = users.map((user, index) => [
        index + 1,
        user.id,
        user.name || 'N/A',
        user.username || 'N/A',
        user.email || 'N/A',
        user.phone || 'N/A',
        user.blocked ? 'Đã khóa' : 'Hoạt động',
        user.referUser?.name || 'N/A',
        new Date(user.createdAt).toLocaleString('vi-VN'),
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map((row) => row.map((cell) => `"${cell}"`).join(','))
        .join('\n');

      return Buffer.from('\uFEFF' + csvContent, 'utf8'); // Add BOM for proper UTF-8 encoding
    } catch (error) {
      console.error('Error in exportUsersToExcel:', error);
      throw error;
    }
  },

  async find(query) {
    return this.listUsers(query);
  },
});

module.exports = userService;
