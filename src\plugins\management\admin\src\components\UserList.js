import React, { useState, useEffect } from 'react';
import {
  Table as AntTable,
  Button as AntButton,
  Space,
  Tag,
  Avatar,
  Tooltip,
  message,
  Modal,
  Empty,
  Pagination,
  Spin,
} from 'antd';
import {
  StopOutlined,
  CheckCircleOutlined,
  TeamOutlined,
  UserAddOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Users, UserCheck, UserX, Download } from 'lucide-react';
import * as XLSX from 'xlsx';
import { useFetchClient } from '@strapi/helper-plugin';
import UserCreateModal from '../components/UserCreateModal';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  SelectInput,
  ActionButtonGroup,
  StyledTable,
} from '../components/shared';

// Status options for user management
const statusOptions = [
  { value: '', label: 'Tất cả', color: '#666', icon: null },
  {
    value: 'active',
    label: '<PERSON>ạt động',
    color: '#52c41a',
    icon: <CheckCircleOutlined />,
  },
  {
    value: 'blocked',
    label: 'Đã khóa',
    color: '#ff4d4f',
    icon: <StopOutlined />,
  },
];

export const UserList = () => {
  const { get, put } = useFetchClient();

  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  const [selectedUser, setSelectedUser] = useState(null);
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);

  const PAGE_SIZE = 10;

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng đại lý',
      value: statistics?.totalUsers || 0,
      icon: Users,
      color: '#3b82f6',
    },
    {
      title: 'Đang hoạt động',
      value: statistics?.activeUsers || 0,
      icon: UserCheck,
      color: '#10b981',
    },
    {
      title: 'Đã khóa',
      value: statistics?.blockedUsers || 0,
      icon: UserX,
      color: '#ef4444',
    },
    {
      title: 'Mới tháng này',
      value: statistics?.newUsersThisMonth || 0,
      icon: TeamOutlined,
      color: '#8b5cf6',
    },
  ];

  const fetchUsers = async (
    currentPage = page,
    currentSearch = search,
    currentStatus = status
  ) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: PAGE_SIZE.toString(),
        ...(currentSearch && { search: currentSearch }),
        ...(currentStatus === 'active' && { blocked: 'false' }),
        ...(currentStatus === 'blocked' && { blocked: 'true' }),
      });

      const response = await get(`/management/users?${queryParams}`);

      setUsers(response.data.data);
      setTotal(response.data.meta.pagination.total);
    } catch (error) {
      console.error('Error fetching users:', error);
      message.error('Không thể tải danh sách đại lý');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (value) => {
    setSearch(value);
    setPage(1);
  };

  // Handle filter change
  const handleFilterChange = (filterType, value) => {
    if (filterType === 'status') {
      setStatus(value);
    }
    setPage(1);
  };

  // Create and download Excel file
  const createAndDownloadExcel = (users) => {
    if (!users.length) {
      message.warning('Không có dữ liệu để xuất');
      return;
    }

    // Prepare Excel data
    const excelData = users.map((user, index) => ({
      STT: index + 1,
      ID: user.id,
      'Tên đại lý': user.name || 'N/A',
      'Tên đăng nhập': user.username || 'N/A',
      Email: user.email || 'N/A',
      'Số điện thoại': user.phone || 'N/A',
      'Số dư': user.balance
        ? new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(user.balance)
        : '0 ₫',
      'Vai trò': user.role?.name || 'N/A',
      'Trạng thái': user.blocked ? 'Đã khóa' : 'Hoạt động',
      'Người giới thiệu': user.referUser?.name || 'N/A',
      'Ngày tạo': new Date(user.createdAt).toLocaleString('vi-VN'),
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 5 }, // STT
      { wch: 8 }, // ID
      { wch: 25 }, // Tên đại lý
      { wch: 20 }, // Tên đăng nhập
      { wch: 30 }, // Email
      { wch: 15 }, // Số điện thoại
      { wch: 20 }, // Số dư
      { wch: 15 }, // Vai trò
      { wch: 15 }, // Trạng thái
      { wch: 25 }, // Người giới thiệu
      { wch: 20 }, // Ngày tạo
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách đại lý');

    // Generate filename with current date
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    const filename = `danh-sach-dai-ly-${dateStr}-${timeStr}.xlsx`;

    // Write and download file
    XLSX.writeFile(workbook, filename);
  };

  // Handle export all users
  const handleBulkExport = async () => {
    try {
      // Prepare query params for export (same as current filters)
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '10000', // Get all users
        ...(search && { search }),
        ...(status === 'active' && { blocked: 'false' }),
        ...(status === 'blocked' && { blocked: 'true' }),
      });

      // Fetch all users with current filters
      const response = await get(`/management/users?${queryParams}`);
      const usersToExport = response.data.data || [];

      if (usersToExport.length === 0) {
        message.warning('Không có đại lý nào phù hợp với bộ lọc đã chọn');
        return;
      }

      // Create and download Excel file
      createAndDownloadExcel(usersToExport);
      message.success(
        `Đã tải xuống file Excel với ${usersToExport.length} đại lý!`
      );
    } catch (error) {
      console.error('Error exporting users:', error);
      message.error('Không thể xuất file Excel');
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await get('/management/users/statistics');
      console.log('User statistics response:', response); // Debug log
      // The backend now returns { success: true, data: { ... } }
      setStatistics(response.data?.data || null);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleStatusChange = async (userId, blocked) => {
    try {
      // Send data in the format controller expects
      await put(`/management/users/${userId}/status`, { blocked });
      message.success(blocked ? 'Đã khóa tài khoản' : 'Đã mở khóa tài khoản');
      fetchUsers();
      fetchStatistics();
    } catch (error) {
      console.error('Error updating user status:', error);
      message.error('Không thể cập nhật trạng thái');
    }
  };

  // Handle user detail click
  const handleUserClick = async (user) => {
    setSelectedUser(user);
    setUserDetailVisible(true);
  };

  useEffect(() => {
    fetchUsers();
    fetchStatistics();
  }, [page, search, status]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (search !== '') {
        fetchUsers(1, search, status);
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  const columns = [
    {
      title: 'Đại lý',
      key: 'user',
      width: 280,
      render: (record) => (
        <Space>
          <Avatar
            src={record.avatar}
            size={48}
            style={{
              backgroundColor: '#f0f2f5',
              color: '#666',
            }}
          >
            {record.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <div>
            <div
              style={{
                fontWeight: 600,
                color: '#1e293b',
                fontSize: '14px',
                cursor: 'pointer',
                transition: 'color 0.2s ease',
              }}
              onClick={() => handleUserClick(record)}
              onMouseEnter={(e) => (e.target.style.color = '#2563eb')}
              onMouseLeave={(e) => (e.target.style.color = '#1e293b')}
            >
              {record.name}
            </div>
            <div style={{ fontSize: '12px', color: '#64748b' }}>
              {record.phone}
            </div>
            <div style={{ fontSize: '12px', color: '#94a3b8' }}>
              {record.email}
            </div>
          </div>
        </Space>
      ),
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Người giới thiệu',
      key: 'referUser',
      width: 180,
      render: (record) => {
        if (record.referUser) {
          return (
            <div>
              <div
                style={{ fontWeight: 500, color: '#1e293b', fontSize: '13px' }}
              >
                {record.referUser.name}
              </div>
              <div style={{ fontSize: '12px', color: '#64748b' }}>
                {record.referUser.phone}
              </div>
            </div>
          );
        }
        return (
          <div
            style={{ color: '#94a3b8', fontSize: '13px', fontStyle: 'italic' }}
          >
            Không có
          </div>
        );
      },
    },
    {
      title: 'Số dư',
      dataIndex: 'balance',
      key: 'balance',
      width: 120,
      render: (value) => (
        <div style={{ color: '#059669', fontSize: '13px', fontWeight: 600 }}>
          {value
            ? new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
              }).format(value)
            : '0 ₫'}
        </div>
      ),
    },
    {
      title: 'Vai trò',
      key: 'role',
      width: 120,
      render: (record) => (
        <Tag
          color="blue"
          style={{
            margin: 0,
            padding: '4px 8px',
            borderRadius: 6,
            fontWeight: 500,
            fontSize: '12px',
          }}
        >
          {record.role?.name || 'N/A'}
        </Tag>
      ),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      width: 160,
      render: (record) => (
        <Space>
          <Tag
            color={record.blocked ? '#ff4d4f' : '#52c41a'}
            icon={record.blocked ? <StopOutlined /> : <CheckCircleOutlined />}
            style={{
              margin: 0,
              padding: '3px 6px',
              borderRadius: 4,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 3,
              width: 'fit-content',
            }}
          >
            {record.blocked ? 'Đã khóa' : 'Hoạt động'}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Ngày tham gia',
      key: 'createdAt',
      width: 120,
      render: (record) => (
        <div style={{ color: '#64748b', fontSize: '13px' }}>
          {new Date(record.createdAt).toLocaleDateString('vi-VN')}
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (record) => (
        <ActionButtonGroup
          onView={() => handleUserClick(record)}
          showView={true}
          showEdit={false}
          showDelete={false}
          viewTooltip="Xem chi tiết đại lý"
          customActions={
            <Tooltip
              title={record.blocked ? 'Mở khóa tài khoản' : 'Khóa tài khoản'}
            >
              <AntButton
                type="text"
                icon={
                  record.blocked ? <CheckCircleOutlined /> : <StopOutlined />
                }
                onClick={() => handleStatusChange(record.id, !record.blocked)}
                style={{
                  color: record.blocked ? '#52c41a' : '#ff4d4f',
                  borderRadius: 6,
                  width: 32,
                  height: 32,
                  padding: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              />
            </Tooltip>
          }
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* User Management */}
        <Card style={{ marginBottom: 24 }}>
          <PageHeader
            title="Quản lý đại lý"
            description="Xem và quản lý danh sách đại lý trong hệ thống"
            actions={
              <Space>
                <Button
                  onClick={() => {
                    setRefreshing(true);
                    fetchUsers();
                    fetchStatistics();
                    setTimeout(() => setRefreshing(false), 1000);
                  }}
                  $variant="outline"
                  disabled={refreshing}
                >
                  <ReloadOutlined
                    className={refreshing ? 'animate-spin' : ''}
                  />
                  Làm mới
                </Button>
                <Button onClick={handleBulkExport} $variant="outline">
                  <Download />
                  Xuất Excel
                </Button>
                <Button
                  onClick={() => setCreateModalVisible(true)}
                  $variant="primary"
                >
                  <UserAddOutlined />
                  Thêm đại lý
                </Button>
              </Space>
            }
          />

          <CardContent>
            {/* Filters Section */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm theo tên, email, số điện thoại..."
                value={search}
                onChange={handleSearch}
              />

              <FilterGroup>
                <FilterLabel>Trạng thái:</FilterLabel>
                <SelectInput
                  value={status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </SelectInput>
              </FilterGroup>
            </FiltersSection>

            {/* Users Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={users}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{
                            color: '#64748b',
                            fontFamily: "'Be Vietnam Pro', sans-serif",
                          }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </StyledTable>

            {/* Pagination */}
            {total > 0 && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 16,
                  padding: '16px 0',
                  borderTop: '1px solid #f0f0f0',
                }}
              >
                <span
                  style={{
                    color: '#64748b',
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                >
                  Hiển thị {(page - 1) * PAGE_SIZE + 1} -{' '}
                  {Math.min(page * PAGE_SIZE, total)} của {total} đại lý
                </span>
                <Pagination
                  current={page}
                  pageSize={PAGE_SIZE}
                  total={total}
                  onChange={(newPage) => {
                    setPage(newPage);
                    fetchUsers(newPage, search, status);
                  }}
                  showSizeChanger={false}
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </Spin>

      {/* User Create Modal */}
      <UserCreateModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={() => {
          setCreateModalVisible(false);
          fetchUsers();
          fetchStatistics();
        }}
      />

      {/* User Detail Modal */}
      <Modal
        title={
          <span
            style={{
              fontWeight: 500,
              fontSize: 16,
              color: '#344054',
              fontFamily: "'Be Vietnam Pro', sans-serif",
            }}
          >
            Chi tiết đại lý
          </span>
        }
        open={userDetailVisible}
        onCancel={() => setUserDetailVisible(false)}
        footer={null}
        width={600}
        style={{ top: 20 }}
      >
        {selectedUser && (
          <div style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Avatar
                src={selectedUser.avatar}
                size={80}
                style={{
                  backgroundColor: '#f0f2f5',
                  color: '#666',
                  marginBottom: 16,
                }}
              >
                {selectedUser.name?.charAt(0)?.toUpperCase()}
              </Avatar>
              <div style={{ fontSize: 18, fontWeight: 600, color: '#1e293b' }}>
                {selectedUser.name}
              </div>
              <div style={{ fontSize: 14, color: '#64748b' }}>
                {selectedUser.role?.name || 'N/A'}
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Tên đăng nhập:
                </span>
                <span style={{ fontWeight: 500 }}>{selectedUser.username}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Email:
                </span>
                <span>{selectedUser.email}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Số điện thoại:
                </span>
                <span>{selectedUser.phone}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Số dư:
                </span>
                <span style={{ color: '#059669', fontWeight: 600 }}>
                  {selectedUser.balance
                    ? new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND',
                      }).format(selectedUser.balance)
                    : '0 ₫'}
                </span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Người giới thiệu:
                </span>
                <span>{selectedUser.referUser?.name || 'Không có'}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Trạng thái:
                </span>
                <Tag
                  color={selectedUser.blocked ? '#ff4d4f' : '#52c41a'}
                  style={{ margin: 0 }}
                >
                  {selectedUser.blocked ? 'Đã khóa' : 'Hoạt động'}
                </Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#6b7280', fontWeight: 500 }}>
                  Ngày tham gia:
                </span>
                <span>
                  {new Date(selectedUser.createdAt).toLocaleDateString('vi-VN')}
                </span>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default UserList;
